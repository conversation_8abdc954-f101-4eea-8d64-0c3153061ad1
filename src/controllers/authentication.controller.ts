import {
	CustomSuccessResponse,
	CustomErrorResponse,
	RefreshTokenSchema,
	type TokenResponse,
	EnumClientOrigin,
	EnumClientPath,
	EnumLogLevel,
	ErrorCodes,
	HttpStatus
} from 'excelytics.shared-internals';
import type {
	RefreshTokenServiceResult,
	RegistrationServiceResult,
	LoginServiceResult
} from '@app-types/service.types';
import { AuthenticationService } from '@/services/authentication.service';
import { RegisterSchema, LoginSchema } from '@/utils/validation.util';
import { hashPassword } from '@/utils/password.utils';
import { JwtService } from '@/services/jwt.service';
import type { Response, Request } from 'express';
import { env_idp } from '@app-types/env_';

export class AuthenticationController {
	private readonly authenticationService: AuthenticationService;
	private readonly jwtService: JwtService;
	private readonly successResponder: CustomSuccessResponse;
	private readonly errorResponder: CustomErrorResponse;

	constructor() {
		this.authenticationService = new AuthenticationService();
		this.jwtService = new JwtService();
		this.successResponder = new CustomSuccessResponse();
		this.errorResponder = new CustomErrorResponse();
	}

	public async register(request: Request, response: Response): Promise<void> {
		// Parse and validate the request body using Zod
		const validationResult = RegisterSchema.safeParse(request.body);
		if (!validationResult.success) {
			console.log('--here--');
			//console.log('response', response);
			//console.log('validationResult.error', validationResult.error);
			this.errorResponder.SendRegistrationValidationError(response, validationResult.error);
			return;
		}

		// Store the original password before hashing
		const { password: originalPassword, ...registrationData } = validationResult.data;
		const hashedPassword = await hashPassword(originalPassword);

		// Extract the validated data & Register the user
		// The service now returns an Identity object, not tokens directly for register
		// Tokens are usually generated upon first login or if register implies login
		const serviceResult: RegistrationServiceResult = await this.authenticationService.registerUser({
			...registrationData,
			password: hashedPassword
		});

		if (!serviceResult.success || !serviceResult.data?.user) {
			this.errorResponder.SendError(
				response,
				serviceResult.error,
				'Registration failed unexpectedly',
				HttpStatus.INTERNAL_SERVER_ERROR,
				ErrorCodes.UNKNOWN_REGISTRATION_ERROR
			);
			return;
		}

		const loginCredentials = {
			email: serviceResult.data.user.email!,
			password: originalPassword,
			clientPath: registrationData.clientPath
		};

		// Auto-login logic
		const loginServiceResult: LoginServiceResult = await this.authenticationService.loginUser(loginCredentials);
		if (!loginServiceResult.success || !loginServiceResult.data) {
			this.successResponder.SendSuccessResponse(
				response,
				'User registered. Please log in.',
				{ userId: serviceResult.data.user.id, email: serviceResult.data.user.email },
				HttpStatus.CREATED // 201
			);
			return;
		}

		const tokenResponseData: TokenResponse = {
			token: loginServiceResult.data.accessToken,
			refreshToken: loginServiceResult.data.refreshToken,
			tokenPayload: loginServiceResult.data.tokenPayload
		};

		this.successResponder.SendTokenSuccessResponse(
			response,
			'User registered and logged in successfully',
			tokenResponseData,
			HttpStatus.CREATED
		);
	}

	public async login(request: Request, response: Response): Promise<void> {
		// Parse and validate the request body using Zod
		const validationResult = LoginSchema.safeParse(request.body);
		if (!validationResult.success) {
			this.errorResponder.SendLoginValidationError(response, validationResult.error);
			return;
		}

		// Extract the validated data & call the loginUser function
		const serviceResult: LoginServiceResult = await this.authenticationService.loginUser(validationResult.data);
		if (!serviceResult.success || !serviceResult.data) {
			this.errorResponder.SendError(
				response,
				serviceResult.error,
				'Login failed unexpectedly.',
				HttpStatus.INTERNAL_SERVER_ERROR,
				ErrorCodes.AUTHENTICATION_FAILED
			);
			return;
		}

		const tokenResponseData: TokenResponse = {
			token: serviceResult.data.accessToken,
			refreshToken: serviceResult.data.refreshToken,
			tokenPayload: serviceResult.data.tokenPayload
		};

		// Log the token response data for debugging
		if (env_idp.LOG_LEVEL === EnumLogLevel.Info) {
			console.log(`User logged in successfully, tokenResponseData: ${JSON.stringify(tokenResponseData)}`);
		}

		this.successResponder.SendTokenSuccessResponse(response, 'Login successful.', tokenResponseData);
	}

	public async refreshToken(request: Request, response: Response): Promise<void> {
		const validationResult = RefreshTokenSchema.safeParse(request.body);
		if (!validationResult.success) {
			this.errorResponder.SendValidationError(response, validationResult.error, 'Invalid refresh token request');
			return;
		}

		const serviceResult: RefreshTokenServiceResult = await this.authenticationService.refreshAccessToken(
			validationResult.data.refreshToken
		);
		if (!serviceResult.success || !serviceResult.data) {
			this.errorResponder.SendError(
				response,
				serviceResult.error,
				'Token refresh failed unexpectedly',
				HttpStatus.INTERNAL_SERVER_ERROR,
				ErrorCodes.TOKEN_REFRESH_FAILED
			);
			return;
		}

		const tokenResponseData: TokenResponse = {
			token: serviceResult.data.accessToken,
			refreshToken: serviceResult.data.newRefreshToken,
			tokenPayload: serviceResult.data.tokenPayload
		};

		this.successResponder.SendTokenSuccessResponse(response, 'Token refreshed successfully', tokenResponseData);
	}

	/**
	 * Get or create session endpoint
	 * Useful for session management and testing
	 */
	async getSession(request: Request, response: Response): Promise<void> {
		try {
			// Ensure session exists (express-session will create one if it doesn't exist)
			if (!request.session) {
				this.errorResponder.SendInternalServerError(
					response,
					'Session middleware not properly configured',
					'SESSION_ERROR'
				);
				return;
			}

			// Force session initialization by setting data
			// This ensures saveUninitialized: false still creates a session
			request.session.data = {
				created: request.session.data?.created || new Date().toISOString(),
				lastAccessed: new Date().toISOString(),
				initialized: true
			};

			// Log session activity for debugging
			if (env_idp.TEST_VERBOSE_LOGGING) {
				console.log(`Session accessed: ${request.session.id}`);
				console.log(`Session cookie settings:`, {
					httpOnly: request.session.cookie.httpOnly,
					secure: request.session.cookie.secure,
					sameSite: request.session.cookie.sameSite,
					maxAge: request.session.cookie.maxAge
				});
			}

			// Return session information (without sensitive data)
			const sessionInfo = {
				sessionId: request.session.id,
				created: request.session.data?.created || new Date().toISOString(),
				lastAccessed: request.session.data?.lastAccessed || new Date().toISOString(),
				initialized: request.session.data?.initialized || false,
				cookie: {
					maxAge: request.session.cookie.maxAge,
					secure: request.session.cookie.secure,
					httpOnly: request.session.cookie.httpOnly,
					sameSite: request.session.cookie.sameSite
				}
			};

			this.successResponder.SendSuccessResponse(response, 'Session retrieved successfully', sessionInfo);
		} catch (error: any) {
			console.error('Error in getSession:', error);
			this.errorResponder.SendInternalServerError(response, 'Failed to retrieve session', 'SESSION_ERROR');
		}
	}

	/**
	 * Generate a short-lived test token (expires in 1 second)
	 * FOR TESTING PURPOSES ONLY - NOT FOR PRODUCTION USE
	 */
	async generateTestToken(request: Request, response: Response): Promise<void> {
		try {
			// Only allow in development/test environments
			if (env_idp.ENV !== 'development' && env_idp.ENV !== 'test') {
				this.errorResponder.SendForbiddenError(
					response,
					'Test token generation only available in development/test environments'
				);
				return;
			}

			// Create a test user payload
			const testPayload = {
				userId: '507f1f77bcf86cd799439011',
				email: '<EMAIL>',
				clientId: '507f1f77bcf86cd799439012',
				clientOrigin: EnumClientOrigin.Excelytics,
				clientPath: EnumClientPath.Identity,
				isActive: true,
				permissions: ['user']
			};

			// Generate token with 1 second expiration
			const shortLivedToken = this.jwtService.generateAccessToken(testPayload, '1s');

			// Log token details for debugging
			if (env_idp.TEST_VERBOSE_LOGGING) {
				console.log('🧪 Generated short-lived test token:');
				console.log(`   Token: ${shortLivedToken.substring(0, 50)}...`);
				console.log(`   Expires in: 1 second`);
				console.log(`   Generated at: ${new Date().toISOString()}`);
			}

			this.successResponder.SendSuccessResponse(response, 'Short-lived test token generated successfully', {
				token: shortLivedToken,
				expiresIn: '1s',
				generatedAt: new Date().toISOString(),
				warning: 'This token expires in 1 second - for testing purposes only'
			});
		} catch (error: any) {
			console.error('Error generating test token:', error);
			this.errorResponder.SendInternalServerError(
				response,
				'Failed to generate test token',
				'TOKEN_GENERATION_ERROR'
			);
		}
	}
}