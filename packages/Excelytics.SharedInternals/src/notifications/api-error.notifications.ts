/**
 * Standardized API error response handling for Excelytics microservices.
 * Provides consistent error formatting across all services that consume this shared package.
 * @module api-error.notifications
 */
import { BaseError, type ErrorDetails } from '../errors';
import { ErrorCodes, HttpStatus } from '../constants';
import type { ErrorResponse } from '../types';
import { env_ } from '../utils/env_internal';
import type { Response } from '../config';
import { EnumEnv } from '../enums';
import { ZodError } from 'zod';

/**
 * Handles formatting and sending standardized error responses for REST APIs.
 * Supports various error types including BaseError, ZodError, and generic errors.
 */
export class CustomErrorResponse {
	/**
	 * Formats Zod validation errors into a clean, readable format for logging
	 * @param zodError - The Zod validation error
	 * @returns Clean string representation of validation errors
	 */
	private formatZodErrorForLogging(zodError: ZodError): string {
		const errors: string[] = [];

		zodError.issues.forEach(issue => {
			const path = issue.path.length > 0 ? issue.path.join('.') : 'root';
			errors.push(`${path}: ${issue.message}`);
		});

		return errors.join(', ');
	}
	/**
	 * Sends a generic error response based on error type
	 * @param response - Express response object
	 * @param error - Error to process (BaseError, Error, or any)
	 * @param defaultMessage - Fallback message if error doesn't provide one
	 * @param defaultStatusCode - HTTP status code to use if not a BaseError
	 * @param defaultErrorCode - Error code to use if not a BaseError
	 * @returns Express response with formatted error
	 */
	public SendError(
		response: Response,
		error?: BaseError | Error | any,
		defaultMessage: string = 'An unexpected error occurred',
		defaultStatusCode: number = HttpStatus.INTERNAL_SERVER_ERROR,
		defaultErrorCode: string = ErrorCodes.UNKNOWN_ERROR
	): Response {
		if (error instanceof BaseError) {
			const apiErrorResponse: ErrorResponse = {
				success: false,
				message: error.message,
				error: error.toApiResponseError()
			};

			return response.status(error.statusCode).json(apiErrorResponse);
		}

		// Log the unknown error for debugging if it's not a BaseError
		console.error('[CustomErrorResponse] Handling non-BaseError:', error);

		const apiErrorResponse: ErrorResponse = {
			success: false,
			message: error instanceof Error ? error.message : defaultMessage,
			error: {
				code: defaultErrorCode,
				message: error instanceof Error ? error.message : defaultMessage,
				details: error instanceof Error ? { name: error.name, stack: error.stack } : error
			}
		};

		return response.status(defaultStatusCode).json(apiErrorResponse);
	}

	/**
	 * Sends a 401 Unauthorized error response
	 * @param response - Express response object
	 * @param message - Custom error message
	 * @returns Express response with 401 status
	 */
	SendUnauthorizedError(
		response: Response,
		message: string = 'Authentication required or action not allowed'
	): Response {
		const apiErrorResponse: ErrorResponse = {
			success: false,
			message: message,
			error: { code: ErrorCodes.UNAUTHORIZED, message }
		};

		return response.status(HttpStatus.UNAUTHORIZED).json(apiErrorResponse);
	}

	/**
	 * Sends a 403 Forbidden error response
	 * @param res - Express response object
	 * @param message - Custom error message
	 * @returns Express response with 403 status
	 */
	public SendForbiddenError(
		res: Response,
		message: string = 'Access to this resource is forbidden'
	): Response {
		const apiErrorResponse: ErrorResponse = {
			success: false,
			message: message,
			error: { code: ErrorCodes.FORBIDDEN, message: message }
		};

		return res.status(HttpStatus.FORBIDDEN).json(apiErrorResponse);
	}

	/**
	 * Sends a 500 Internal Server Error response
	 * @param response - Express response object
	 * @param originalError - Original error that caused the server error
	 * @param message - Custom error message
	 * @returns Express response with 500 status
	 */
	SendInternalServerError(
		response: Response,
		originalError?: any,
		message: string = 'An internal servererror occurred'
	): Response {
		if (originalError) {
			console.error('[ServerError]', message, originalError);
		} else {
			console.error('[ServerError]', message);
		}

		const apiErrorResponse: ErrorResponse = {
			success: false,
			message: message,
			error: {
				code: ErrorCodes.INTERNAL_SERVER_ERROR,
				message: message,
				// Avoid sending full error details to client in prod for server errors
				details: env_.ENV === EnumEnv.Development && originalError
					? { name: originalError.name, message: originalError.message } : undefined
			}
		};

		return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json(apiErrorResponse);
	}

	/**
	 * Sends a 400 Bad Request error response
	 * @param response - Express response object
	 * @param message - Custom error message
	 * @param details - Additional error details
	 * @returns Express response with 400 status
	 */
	SendBadRequestError(
		response: Response,
		message: string = 'The request could not be understood or was missing required parameters',
		details?: ErrorDetails | any,
	): Response {
		const apiErrorResponse: ErrorResponse = {
			success: false,
			message: message,
			error: { code: ErrorCodes.BAD_REQUEST, message: message, details: details }
		};

		return response.status(HttpStatus.BAD_REQUEST).json(apiErrorResponse);
	}

	/**
	 * Sends a 404 Not Found error response
	 * @param response - Express response object
	 * @param message - Custom error message
	 * @returns Express response with 404 status
	 */
	public SendNotFoundError(
		response: Response,
		message: string = 'The requested resource was not found'
	): Response {
		const apiErrorResponse: ErrorResponse = {
			success: false,
			message: message,
			error: { code: ErrorCodes.NOT_FOUND, message: message }
		};

		return response.status(HttpStatus.NOT_FOUND).json(apiErrorResponse);
	}

	/**
	 * Handles Zod validation errors with detailed formatting
	 * @param response - Express response object
	 * @param zodError - Zod validation error
	 * @param message - Custom error message
	 * @param errorCode - Specific error code for this validation error
	 * @returns Express response with 400 status and formatted validation errors
	 */
	public SendValidationError(
		response: Response,
		zodError: ZodError,
		message: string = 'Input validation failed',
		errorCode: string = ErrorCodes.VALIDATION_ERROR
	): Response {
		// Create a clean, readable error summary for logging
		const errorSummary = this.formatZodErrorForLogging(zodError);
		console.warn(`[ValidationError] ${message}:`, errorSummary);

		const apiErrorResponse: ErrorResponse = {
			success: false,
			message: message,
			error: {
				code: errorCode,
				message: 'One or more fields failed validation',
				details: zodError.format() // Zod' structured errors
			}
		};

		// Or HttpStatus.UNPROCESSABLE_ENTITY (422)
		return response.status(HttpStatus.BAD_REQUEST).json(apiErrorResponse);
	}

	/**
	 * Handles registration-specific validation errors
	 * @param response - Express response object
	 * @param error - Zod validation error
	 * @returns Express response with formatted validation errors
	 */
	SendRegistrationValidationError(
		response: Response,
		error: ZodError
	): Response {
		return this.SendValidationError(
			response,
			error,
			'Registration failed due to validation errors',
			ErrorCodes.REGISTRATION_VALIDATION_ERROR
		);
	}

	/**
	 * Handles login-specific validation errors
	 * @param response - Express response object
	 * @param error - Zod validation error
	 * @returns Express response with formatted validation errors
	 */
	SendLoginValidationError(
		response: Response,
		error: ZodError
	): Response {
		return this.SendValidationError(
			response,
			error,
			'Login failed due to validation errors.',
			ErrorCodes.LOGIN_VALIDATION_ERROR
		);
	}

	/**
	 * Specialized handler for registration errors including duplicate key errors
	 * @param response - Express response object
	 * @param error - Any error that occurred during registration
	 * @returns Express response with appropriate status and error details
	 */
	HandleRegistrationError(response: Response, error: any): Response {
		console.error('[HandleRegistrationError]', error);

		// Handle potential duplicate key errors (e.g. email) - MongoDB duplicate key
		if (error.code === 11000 || (error.message && error.message.includes('duplicate key'))) {
			const field = error.keyPattern ? Object.keys(error.keyPattern)[0] : 'unique field';
			const errorMessage = `An account with this ${field} already exists.`;

			const apiErrorResponse: ErrorResponse = {
				success: false,
				message: errorMessage,
				error: {
					code: ErrorCodes.DUPLICATE_KEY_ERROR,
					message: errorMessage,
					details: { field }
				}
			};

			// 409 Conflict
			return response.status(HttpStatus.CONFLICT).json(apiErrorResponse);
		}

		// If it's already a BaseError from the service (e.g., a custom registration failure)
		if (error instanceof BaseError) {
			return this.SendError(response, error);
		}

		// Fallback for other errors during registration
		return this.SendInternalServerError(
			response,
			error,
			'An unexpected error occurred during registration');
	}
}